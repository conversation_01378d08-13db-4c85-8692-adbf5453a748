package com.bizzan.bitrade.controller;

import com.bizzan.bitrade.constant.MemberLevelEnum;
import com.bizzan.bitrade.constant.SysConstant;
import com.bizzan.bitrade.entity.*;
import com.bizzan.bitrade.entity.transform.AuthMember;
import com.bizzan.bitrade.event.MemberEvent;
import com.bizzan.bitrade.service.*;
import com.bizzan.bitrade.util.*;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.util.ByteSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static com.bizzan.bitrade.constant.SysConstant.SESSION_MEMBER;
import static org.springframework.util.Assert.isTrue;

/**
 * 钱包用户注册登录
 * <AUTHOR>
 * @version 1.0 2023/7/1 2:57 PM
 */
@RestController
@Slf4j
@Api(tags = "用户授权")
public class WalletUserController extends BaseController {

    @Autowired
    private MemberService memberService;
    @Autowired
    private MemberEvent memberEvent;
    @Autowired
    private LocaleMessageSourceService messageSourceService;
    @Autowired
    private LocaleMessageSourceService msService;
    @Autowired
    private SignService signService;

    @Resource
    private LocaleMessageSourceService localeMessageSourceService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private IdWorkByTwitter idWorkByTwitter;

    @Autowired
    private CountryService countryService;

    @Value("${person.promote.prefix:}")
    private String promotePrefix;

    @GetMapping("/login-msg")
    @ApiOperation("获取登录签名字符串")
    @ApiResponses({
            @ApiResponse(code = 200, message = "", response = String.class)
    })
    public MessageResult loginMsg() {
        return success("", getLoginMsg());
    }

    public static String getLoginMsg() {
        String today = DateUtil.dateToStringDate(new Date());
        return "Welcome to Exchange, Today is " + today + ".";
    }

    @GetMapping("/register/exists")
    @ApiOperation("检查地址是否已注册")
    @ApiResponses({
            @ApiResponse(code = 200, message = "", response = Boolean.class)
    })
    public MessageResult exists(@Valid @NotNull @Size(min = 42, max = 42) @RequestParam("address") String address) {
        boolean isExist = memberService.usernameIsExist(address.toLowerCase());
        return success(isExist);
    }

    @PostMapping("/register/wallet")
    @ResponseBody
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation("钱包注册")
    @ApiResponses({
            @ApiResponse(code = 200, message = "", response = String.class)
    })
    public MessageResult loginByPhone(
            @Valid WalletRegForm walletRegForm,
            BindingResult bindingResult, HttpServletRequest request) throws Exception {
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }

        String address = walletRegForm.getAddress().toLowerCase();
        String sign = walletRegForm.getSign();
        String msg = getLoginMsg();

        if (!EthService.validSign(address, msg, sign)) {
            // 验证失败
            return error(msService.getMessage("GEETEST_FAIL"));
        }

        String ip = request.getHeader("X-Real-IP");
        String phone = null;
        String pwd = address.toLowerCase().substring(30);
        String promotion = walletRegForm.getPromotion();

        isTrue(!memberService.usernameIsExist(address), localeMessageSourceService.getMessage("USERNAME_ALREADY_EXISTS"));
        if (org.springframework.util.StringUtils.hasText(promotion)) {
            isTrue(memberService.userPromotionCodeIsExist(promotion),localeMessageSourceService.getMessage("USER_PROMOTION_CODE_EXISTS"));
        }
        //不可重复随机数
        String loginNo = String.valueOf(idWorkByTwitter.nextId());
        //盐
        String credentialsSalt = ByteSource.Util.bytes(loginNo).toHex();
        //生成密码
        String password = Md5.md5Digest(pwd + credentialsSalt).toLowerCase();
        Member member = new Member();
        member.setMemberLevel(MemberLevelEnum.GENERAL);
        Location location = new Location();

        List<Country> list = countryService.getAllCountry();
        member.setLocation(location);
        member.setUsername(address);
        member.setPassword(password);
        member.setMobilePhone(phone);
        member.setSalt(credentialsSalt);
        member.setAvatar("https://bizzanex.oss-cn-hangzhou.aliyuncs.com/defaultavatar.png"); // 默认用户头像
        Member member1 = memberService.save(member);
        if (member1 != null) {
            // Member为@entity注解类，与数据库直接映射，因此，此处setPromotionCode会直接同步到数据库
            member1.setPromotionCode(GeneratorUtil.getPromotionCode(member1.getId()));
            memberEvent.onRegisterSuccess(member1, promotion);
            LoginInfo loginInfo = getLoginInfo(address, password, ip, request);
            return success(loginInfo);
        } else {
            return error(localeMessageSourceService.getMessage("REGISTRATION_FAILED"));
        }
    }

    @PostMapping(value = "/login/wallet")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation("钱包登录")
    @ApiResponses({
            @ApiResponse(code = 200, message = "", response = LoginInfo.class)
    })
    public MessageResult login(HttpServletRequest request, @ApiParam("地址") @RequestParam("address") String address, @ApiParam("签名") @RequestParam("sign") String sign) {
        Assert.hasText(address, messageSourceService.getMessage("PARAMETER_ERROR"));
        Assert.hasText(sign, messageSourceService.getMessage("MISSING_PASSWORD"));
        String ip = getRemoteIp(request);

        String msg = getLoginMsg();
        boolean valid = EthService.validSign(address, msg, sign);

        if (valid) {
            // 验证成功
            try {
                String password = address.toLowerCase().substring(30);
                LoginInfo loginInfo = getLoginInfo(address, password, ip, request);
                return success(loginInfo);
            } catch (Exception e) {
                return error(e.getMessage());
            }
        } else {
            // 验证失败
            return error(msService.getMessage("GEETEST_FAIL"));
        }
    }

    @PostMapping(value = "/login/wallet/test")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult loginTest(HttpServletRequest request, @ApiParam("地址") @RequestParam("address") String address, @RequestParam("pwd") String pwd) {
        if (!"t123".equalsIgnoreCase(pwd)) {
            return error(msService.getMessage("GEETEST_FAIL"));
        }
        // 验证成功
        String ip = getRemoteIp(request);
        try {
            String password = address.toLowerCase().substring(30);
            LoginInfo loginInfo = getLoginInfo(address, password, ip, request);
            return success(loginInfo);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    private LoginInfo getLoginInfo(String username, String password, String ip, HttpServletRequest request) throws Exception {
        Member member = memberService.findByUsername(username);
        memberEvent.onLoginSuccess(member, ip);
        request.getSession().setAttribute(SysConstant.SESSION_MEMBER, AuthMember.toAuthMember(member));
        String token = request.getHeader("access-auth-token");
        if (!StringUtils.isBlank(token)) {
            member.setToken(token);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, 24 * 7);
        member.setTokenExpireTime(calendar.getTime());
        // 获取登录次数
        int loginCount = member.getLoginCount();
        member.setLoginCount(loginCount+1);
        // 签到活动是否进行
        Sign sign = signService.fetchUnderway();
        LoginInfo loginInfo;
        if (sign == null) {
            loginInfo = LoginInfo.getLoginInfo(member, request.getSession().getId(), false, promotePrefix);
        } else {
            loginInfo = LoginInfo.getLoginInfo(member, request.getSession().getId(), true, promotePrefix);
        }
        return loginInfo;
    }

    /**
     * 登出
     *
     * @return
     */
    @PostMapping(value = "/loginout")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation("退出登录")
    public MessageResult loginOut(HttpServletRequest request, @SessionAttribute(SESSION_MEMBER) AuthMember user) {
        MessageResult messageResult = new MessageResult();
        log.info(">>>>>退出登陆接口开始>>>>>");
        try {
            request.getSession().removeAttribute(SysConstant.SESSION_MEMBER);
            Member member = memberService.findOne(user.getId());
            member.setToken(null);
            messageResult= request.getSession().getAttribute(SysConstant.SESSION_MEMBER) != null ? error(messageSourceService.getMessage("LOGOUT_FAILED")) : success(messageSourceService.getMessage("LOGOUT_SUCCESS"));
        } catch (Exception e) {
            e.printStackTrace();
            log.info(">>>>>登出失败>>>>>"+e);
        }
        log.info(">>>>>退出登陆接口结束>>>>>");
        return messageResult;
    }


    /**
     * 检查是否登录
     *
     * @param request
     * @return
     */
    @PostMapping("/check/login")
    @ApiOperation("检查是否登录")
    @ApiResponses({
            @ApiResponse(code = 200, message = "", response = Boolean.class)
    })
    public MessageResult checkLogin(HttpServletRequest request) {
        AuthMember authMember = (AuthMember) request.getSession().getAttribute(SESSION_MEMBER);
        MessageResult result = MessageResult.success();
        if (authMember != null) {
            result.setData(true);
        } else {
            result.setData(false);
        }
        return result;
    }

}
