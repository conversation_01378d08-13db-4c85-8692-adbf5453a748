package com.bizzan.bitrade.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 *
 * <AUTHOR>
 * @version 1.0 2023/7/1 5:48 PM
 */
@Data
public class WalletRegForm {

    @NotNull
    @ApiModelProperty("地址")
    @Size(max = 42, min = 42)
    String address;
    @ApiModelProperty("签名")
    @NotNull
    String sign;
    @ApiModelProperty("推广码")
    String promotion;
}
