logback.path=./logs

#Euruka
eureka.client.serviceUrl.defaultZone=http://**********:7000/eureka/
eureka.instance.prefer-ip-address=true
eureka.instance.instance-id=${spring.cloud.client.ipAddress}:${spring.application.name}:${server.port}

#Mysql
spring.datasource.url=jdbc:mysql://**********:3306/exchange?characterEncoding=utf-8&useSSL=false
spring.datasource.username=exchange
spring.datasource.password=NjfKttzTsrWeJ8jA1

#MongoDB
spring.data.mongodb.uri=mongodb://**********:27017/bizzan
spring.data.mongodb.database=bizzan

#Redis
spring.redis.host=**********
spring.redis.port=6379
spring.redis.password=dmsLjNt3J5yfCydN

#Kafka
spring.kafka.bootstrap-servers=**********:9092

#Aliyun OSS config
aliyun.accessKeyId=LTAI5tNCaVqRyHj3uHuXkux9
aliyun.accessKeySecret=******************************
#外网地址
aliyun.ossEndpoint=oss-cn-hangzhou.aliyuncs.com
aliyun.ossBucketName=bizzanss

#Druid监控登录账户
druidlogin-username=root
#Druid监控登录密码
druidlogin-password=admin666

#SMS
sms.driver=saiyou
sms.gateway=https://live.moduyun.com/sms/v1/sendsinglesms
sms.username=5f598c13efb9a3119b148e94
sms.password=5a5b13e4304949eda2432a4cdc83a3a5
sms.sign=融华
sms.internationalGateway=
sms.internationalUsername=
sms.internationalPassword=

#创瑞短信
access.key.id=
access.key.secret=

#E-Mail Setting
spring.mail.host=smtp.126.com
spring.mail.port=465
spring.mail.username=<EMAIL>
spring.mail.password=BTXchain199696
spark.system.host=smtp.126.com
spark.system.name=BTXCHAIN
#接收系统通知的邮箱，多个用【,】分割
spark.system.admins=<EMAIL>
#通知短信接收手机，多个用【,】分割
spark.system.admin-phones=13045778437

#阿里云 邮件 & 短信 配置
aliyun.mail-sms.region=cn-hangzhou
aliyun.mail-sms.access-key-id=LTAI5tAMrYb5ND3Kjv9YcMZx
aliyun.mail-sms.access-secret=******************************
aliyun.mail-sms.from-address=<EMAIL>
aliyun.mail-sms.from-alias=yangquan
aliyun.mail-sms.sms-sign=yangquan
aliyun.mail-sms.sms-template=SMS_220360224
aliyun.mail-sms.email-tag=yangquan

#Actuator监控配置（可选）
management.context-path=/monitor
management.security.enabled=false
security.user.name=test
security.user.password=admin666

#推荐注册奖励:如果下面配置为1，被推荐人必须实名认证推荐人才可获得奖励，否则没有限制，注意与admin模块里面的配置保持统一
commission.need.real-name=1
#开启二级奖励（1=开启）
commission.promotion.second-level=1
#个人推广链接的前缀，随着登录接口返回给客户端。客户端那边与推广码连接，组成个人推广链接。如果有推广注册功能必填
person.promote.prefix=https://www.bizzans.com/#/reg?code=
#转账接口地址
transfer.url=
transfer.key=
transfer.smac=

#ES配置文件
es.username=
es.password=
es.mine.index=
es.mine.type=
es.public.ip=
es.private.ip=#
es.port=9092