#user  nobody;
worker_processes  auto;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  10240;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;
    gzip  on;
    gzip_min_length 1000;
    gzip_http_version 1.1;
    gzip_vary on;
    gzip_types text/plain text/css image/jpeg image/gif image/png application/javascript application/json;
    server_tokens off;

    #gzip  on;

    server {
        listen       10080;
        server_name  localhost;

        #charset koi8-r;

        #access_log  logs/host.access.log  main;

        root   /home/<USER>/h5;

        error_page  404              /404.html;

        location / {
            #root   html;
            index  index.html index.htm;
            etag off;
            header_filter_by_lua_block {
                if string.find(ngx.var.uri, "^/assets") == 1 then
                    ngx.header['X-Type'] = "asset"
                else
                    ngx.header['X-Type'] = "other"
                    ngx.header['Last-Modified'] = ""
                    ngx.header['Cache-Control'] = "no-cache"
                end
            }

            try_files $uri $uri/ /index.html;
        }

        #location ~ ^/market/|^/uc/|^/exchange/|^/swap/|^/admin/|^/ermarket/ {
        #   client_max_body_size    5m;
        #    set_by_lua_file $target /home/<USER>/router.lua;
        #    header_filter_by_lua_file /home/<USER>/header.lua;
        #   proxy_pass http://$target;
        #   proxy_set_header Host $http_host;
        #   proxy_set_header X-Real-IP $remote_addr;
        #   proxy_set_header X-Scheme $scheme;
        #   proxy_set_header Upgrade $http_upgrade;
        #   proxy_set_header Connection "upgrade";
        #}
        location /exchange {
            client_max_body_size    5m;
            proxy_pass http://127.0.0.1:6003;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        location /uc {
            client_max_body_size    5m;
            proxy_pass http://127.0.0.1:6001;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        location /swap {
            client_max_body_size    5m;
            #proxy_pass http://*************:6012;
            proxy_pass http://127.0.0.1:6012;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Scheme $scheme;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
        #location /admin {
        #    client_max_body_size    5m;
        #    proxy_pass http://127.0.0.1:6010;
        #    proxy_set_header Host $host;
        #    proxy_set_header X-Real-IP $remote_addr;
        #    header_filter_by_lua_block {
        #        ngx.header["X-Application-Context"] = nil
        #        local cookie = ngx.header["Set-Cookie"]
        #        if type(cookie) == 'string' then
        #            cookie = cookie .. ";HttpOnly;Secure; SameSite=None"
        #        end
        #        if type(tookie) == 'table' then
        #            for _,v in ipairs(cookie) do
        #                ngx.log(ngx.ERR, '----', tostring(v))
        #            end
        #            -- cookie = cookie .. ";HttpOnly;Secure; SameSite=None"
        #        end
        #        ngx.header["Set-Cookie"] = cookie
        #    }
        #}

        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }

        # proxy the PHP scripts to Apache listening on 127.0.0.1:80
        #
        #location ~ \.php$ {
        #    proxy_pass   http://127.0.0.1;
        #}

        # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
        #
        #location ~ \.php$ {
        #    root           html;
        #    fastcgi_pass   127.0.0.1:9000;
        #    fastcgi_index  index.php;
        #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
        #    include        fastcgi_params;
        #}

        # deny access to .htaccess files, if Apache's document root
        # concurs with nginx's one
        #
        #location ~ /\.ht {
        #    deny  all;
        #}
    }


    # another virtual host using mix of IP-, name-, and port-based configuration
    #
    #server {
    #    listen       8000;
    #    listen       somename:8080;
    #    server_name  somename  alias  another.alias;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}


    # HTTPS server
    #
    #server {
    #    listen       443 ssl;
    #    server_name  localhost;

    #    ssl_certificate      cert.pem;
    #    ssl_certificate_key  cert.key;

    #    ssl_session_cache    shared:SSL:1m;
    #    ssl_session_timeout  5m;

    #    ssl_ciphers  HIGH:!aNULL:!MD5;
    #    ssl_prefer_server_ciphers  on;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}

}
