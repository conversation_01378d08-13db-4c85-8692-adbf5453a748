/*
 * Copyright (c) 2017-2018 阿期米德 All Rights Reserved.
 * <AUTHOR> QQ:450099136
 * @Date: 2020/3/21 17:30
 * @Version: 1.0
 * History:
 * v1.0.0, sanfeng,  2020/3/21 17:30, Create
 */
package com.bizzan.aqmd.service;


/**
 * <p>Description: </p>
 *
 * <AUTHOR> QQ:450099136
 * @Date: 2020/3/21 17:30
 */
public class DefaultChannelEventDealService implements ChannelEventDealService {
    @Override
    public void dealChannelActive(String serverIp, String clientIp, int clientPort) {

    }

    @Override
    public void dealChannelDestory(String serverIp, String clientIp, int clientPort) {

    }
}