package com.bizzan.bitrade.vo;

import java.math.BigDecimal;

public class RobotParams {
    private String coinName = ""; // 如btcusdt
    private boolean isHalt = true; // 是否暂停状态
    private double startAmount = 0.001; // 最低交易量
    private double randRange0 = 20; // 交易量随机数范围 1%概率
    private double randRange1 = 4; // 交易量随机数范围 9%概率
    private double randRange2 = 1; //  交易量随机数范围0.1(0.0001 ~ 0.09) 20%概率
    private double randRange3 = 0.1; // 交易量随机数范围0.1(0.0001 ~ 0.09) 20%概率
    private double randRange4 = 0.01; // 交易量随机数范围0.1(0.0001 ~ 0.09) 20%概率
    private double randRange5 = 0.001; // 交易量随机数范围0.1(0.0001 ~ 0.09) 20%概率
    private double randRange6 = 0.0001; // 交易量随机数范围0.1(0.0001 ~ 0.09) 10%概率
    private int scale = 4;//价格精度要求
    private int amountScale = 6; // 数量精度要求
    private BigDecimal maxSubPrice = new BigDecimal(20); // 买盘最高价与卖盘最低价相差超过20美金
    private int initOrderCount = 30; // 初始订单数量（此数字必须大于24）
    private BigDecimal priceStepRate = new BigDecimal(0.003); // 价格变化步长(0.01 = 1%)
    private int runTime = 1000; // 行情请求间隔时间（5000 = 5秒）

    private int robotType = 0; // 机器人类型
    private int strategyType = 1; // 控盘机器人策略（1：跟随，2：自定义）
    private String flowPair = "BTC/USDT"; // 跟随交易对
    private BigDecimal flowPercent = BigDecimal.valueOf(1); // 跟随比例

    public BigDecimal getFlowPercent() {
        return flowPercent;
    }

    public void setFlowPercent(BigDecimal flowPercent) {
        this.flowPercent = flowPercent;
    }

    public String getFlowPair() { return flowPair; }
    public void setFlowPair(String flowPair) { this.flowPair = flowPair; }

    public int getStrategyType() { return strategyType; }
    public void setStrategyType(int strategyType) { this.strategyType = strategyType; }

    public int getRobotType() {
        return robotType;
    }
    public void setRobotType(int robotType) {
        this.robotType = robotType;
    }

    public double getStartAmount() {
        return startAmount;
    }
    public void setStartAmount(double startAmount) {
        this.startAmount = startAmount;
    }
    public double getRandRange0() {
        return randRange0;
    }
    public void setRandRange0(double randRange0) {
        this.randRange0 = randRange0;
    }
    public double getRandRange1() {
        return randRange1;
    }
    public void setRandRange1(double randRange1) {
        this.randRange1 = randRange1;
    }
    public double getRandRange2() {
        return randRange2;
    }
    public void setRandRange2(double randRange2) {
        this.randRange2 = randRange2;
    }
    public double getRandRange3() {
        return randRange3;
    }
    public void setRandRange3(double randRange3) {
        this.randRange3 = randRange3;
    }
    public double getRandRange4() {
        return randRange4;
    }
    public void setRandRange4(double randRange4) {
        this.randRange4 = randRange4;
    }
    public double getRandRange5() {
        return randRange5;
    }
    public void setRandRange5(double randRange5) {
        this.randRange5 = randRange5;
    }
    public double getRandRange6() {
        return randRange6;
    }
    public void setRandRange6(double randRange6) {
        this.randRange6 = randRange6;
    }
    public int getScale() {
        return scale;
    }
    public void setScale(int scale) {
        this.scale = scale;
    }
    public int getAmountScale() {
        return amountScale;
    }
    public void setAmountScale(int amountScale) {
        this.amountScale = amountScale;
    }
    public BigDecimal getMaxSubPrice() {
        return maxSubPrice;
    }
    public void setMaxSubPrice(BigDecimal maxSubPrice) {
        this.maxSubPrice = maxSubPrice;
    }
    public int getInitOrderCount() {
        return initOrderCount;
    }
    public void setInitOrderCount(int initOrderCount) {
        this.initOrderCount = initOrderCount;
    }
    public BigDecimal getPriceStepRate() {
        return priceStepRate;
    }
    public void setPriceStepRate(BigDecimal priceStepRate) {
        this.priceStepRate = priceStepRate;
    }
    public int getRunTime() {
        return runTime;
    }
    public void setRunTime(int runTime) {
        this.runTime = runTime;
    }
    public String getCoinName() {
        return coinName;
    }
    public void setCoinName(String coinName) {
        this.coinName = coinName;
    }
    public boolean isHalt() {
        return isHalt;
    }
    public void setHalt(boolean isHalt) {
        this.isHalt = isHalt;
    }
}