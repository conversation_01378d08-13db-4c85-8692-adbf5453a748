package com.bizzan.bitrade.controller;


import com.bizzan.bitrade.util.MessageResult;
import com.bizzan.bitrade.vo.RobotParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/robot")
public class RobotController extends BaseController{

    @Autowired
    private MongoTemplate mongoTemplate;

    @RequestMapping("/params/{symbol}")
    public MessageResult params(@PathVariable("symbol") String symbol) {
        Query query = new Query();
        Criteria criteria = Criteria.where("coinName").is(symbol);
        query.addCriteria(criteria);
        RobotParams robotParams = mongoTemplate.findOne(query, RobotParams.class, "robot_params");
        return success(robotParams);
    }

}
