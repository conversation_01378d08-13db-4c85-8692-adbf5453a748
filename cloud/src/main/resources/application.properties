server.port=7000
eureka.instance.instance-id=${spring.cloud.client.ipAddress}:${server.port}
eureka.instance.hostname=${spring.cloud.client.ipAddress}
eureka.client.registerWithEureka=false
eureka.client.fetchRegistry=false
eureka.client.serviceUrl.defaultZone=http://${eureka.instance.hostname}:${server.port}/eureka/
eureka.server.enable-self-preservation=true
eureka.instance.prefer-ip-address=true

#Mail Setting
spring.mail.host=@spring.mail.host@
spring.mail.port=@spring.mail.port@
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
spring.mail.username=@spring.mail.username@
spring.mail.password=@spring.mail.password@
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
#SYSTEM(发送邮件使用)
spark.system.work-id=1
spark.system.data-center-id=1
spark.system.host=@spring.mail.host@
spark.system.name=@spark.system.name@
#接收系统通知的邮箱，多个用【,】分割
spark.system.admins=@spark.system.admins@
#通知短信接收手机
spark.system.admin-phones=@spark.system.admin-phones@