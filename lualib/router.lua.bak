-- 
-- -- 路由配置表
-- local routes = {
--     -- 用户中心服务
--     ["/uc"] = {
--         hosts = {"127.0.0.1:6001"},
--         description = "用户中心服务"
--     },
--     -- 交易所服务
--     ["/exchange"] = {
--         hosts = {"127.0.0.1:6003"},
--         description = "交易所服务"
--     },
--     -- 合约交易服务
--     ["/swap"] = {
--         hosts = {"127.0.0.1:6012"},
--         description = "合约交易服务"
--     },
--     -- 市场数据服务
--     ["/market"] = {
--         hosts = {"127.0.0.1:6004"},
--         description = "市场数据服务"
--     },
--     -- 管理后台服务
--     ["/admin"] = {
--         hosts = {"127.0.0.1:6005"},
--         description = "管理后台服务"
--     },
--     -- 钱包服务
--     ["/wallet"] = {
--         hosts = {"127.0.0.1:6006"},
--         description = "钱包服务"
--     },
--     -- API网关服务
--     ["/api"] = {
--         hosts = {"127.0.0.1:6007"},
--         description = "API网关服务"
--     }
-- }
-- 
-- -- 默认路由配置
-- local default_route = {
--     host = "127.0.0.1:6001",
--     description = "默认路由到用户中心"
-- }
-- 
-- -- 负载均衡策略：轮询
-- local function round_robin(hosts)
--     if not hosts or #hosts == 0 then
--         return nil
--     end
-- 
--     if #hosts == 1 then
--         return hosts[1]
--     end
-- 
--     -- 使用时间戳进行简单的轮询
--     local index = (ngx.time() % #hosts) + 1
--     return hosts[index]
-- end
-- 
-- -- 负载均衡策略：随机
-- local function random_select(hosts)
--     if not hosts or #hosts == 0 then
--         return nil
--     end
-- 
--     if #hosts == 1 then
--         return hosts[1]
--     end
-- 
--     math.randomseed(ngx.time())
--     local index = math.random(1, #hosts)
--     return hosts[index]
-- end
-- 
-- -- 负载均衡策略：基于IP哈希
-- local function ip_hash(hosts, client_ip)
--     if not hosts or #hosts == 0 then
--         return nil
--     end
-- 
--     if #hosts == 1 then
--         return hosts[1]
--     end
-- 
--     -- 简单的IP哈希算法
--     local hash = 0
--     for i = 1, #client_ip do
--         hash = hash + string.byte(client_ip, i)
--     end
-- 
--     local index = (hash % #hosts) + 1
--     return hosts[index]
-- end
-- 
-- -- 主路由函数
-- local function route_request()
--     -- 获取请求URI
--     local uri = ngx.var.uri or ""
--     local client_ip = ngx.var.remote_addr or "unknown"
--     local method = ngx.var.request_method or "GET"
-- 
--     -- 记录请求信息
--     ngx.log(ngx.ERR, "Router: Processing request - URI: " .. uri .. ", IP: " .. client_ip .. ", Method: " .. method)
-- 
--     -- 查找匹配的路由
--     local matched_route = nil
--     local matched_prefix = ""
-- 
--     for prefix, route_config in pairs(routes) do
--         if string.find(uri, "^" .. prefix) == 1 then
--             -- 找到最长匹配的前缀
--             if #prefix > #matched_prefix then
--                 matched_route = route_config
--                 matched_prefix = prefix
--             end
--         end
--     end
-- 
--     local target_host = nil
--     local route_description = ""
-- 
--     if matched_route then
--         -- 使用负载均衡策略选择后端服务器
--         -- 这里使用轮询策略，您可以根据需要修改为其他策略
--         target_host = round_robin(matched_route.hosts)
--         route_description = matched_route.description
-- 
--         ngx.log(ngx.INFO, "Router: Matched route - Prefix: " .. matched_prefix ..
--                 ", Target: " .. (target_host or "none") ..
--                 ", Description: " .. route_description)
--     else
--         -- 使用默认路由
--         target_host = default_route.host
--         route_description = default_route.description
-- 
--         ngx.log(ngx.INFO, "Router: Using default route - Target: " .. target_host ..
--                 ", Description: " .. route_description)
--     end
-- 
--     -- 健康检查（可选）
--     -- 这里可以添加对目标服务器的健康检查逻辑
-- 
--     -- 设置响应头，便于调试
--     ngx.header["X-Route-Target"] = target_host
--     ngx.header["X-Route-Description"] = route_description
--     ngx.header["X-Route-Prefix"] = matched_prefix
-- 
--     return target_host
-- end
-- 
-- -- 执行路由并返回结果
-- return route_request()

--ngx.log(ngx.ERR, "Router: Processing request - URI: " .. ngx.var.uri .. ", IP: " .. ngx.var.remote_addr .. ", Method: " .. ngx.var.request_method)

return "abc123"
